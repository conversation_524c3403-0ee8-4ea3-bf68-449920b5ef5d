<template>
  <div style="display: flex;justify-content: center;margin-top: 20px;">
    <div id="addAbilit" class="background_fff" style="width:1200px">
      <div>
        <!-- <div v-if="hasReStar" class="tip" style="
          display: flex;
          margin: 20px;
          border: 1px solid #f51d0f;
          padding: 16px 24px;
          border-radius: 8px 8px 8px 8px;
          margin-bottom: 24px;
          align-items: center;
          background: rgba(245, 29, 15, 0.1);
        ">
          <img width="28px" height="28px" class="margin_r_16" src="@/assets/images/warning.png" />
          <div>
            <span>抱歉，您的审核未通过</span>
            <p class="content" style="margin-bottom: 0">{{ auditReason }}</p>
          </div>
        </div> -->
        <div class="loading-overlay" v-if="viewLoading">
          <a-spin :spinning="viewLoading" tip="附件加载中"></a-spin>
        </div>
        <div class="loading-overlay" v-if="formLoading">
          <a-spin :spinning="formLoading" tip="数据加载中..." />
        </div>
        <a-form ref="mainFormRef" :model="formData" labelAlign="right" :rules="rules"
          class="operation padding_l_24 padding_t_24">
          <p class="group_title weight500 font_14 font_00060e">
            <span class="icon"></span>工单信息
          </p>
          <a-row>
            <a-col :span="10">
              <a-form-item label="工单标题">
                {{ formData.title }}
              </a-form-item>
            </a-col>
          </a-row>
          <p class="group_title weight500 font_14 font_00060e">
            <span class="icon"></span>基础信息
          </p>
          <div class="base_info">
            <a-row>
              <a-col :span="12">
                <a-form-item label="项目名称">
                  {{ formData.projectName }}
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="项目编码">
                  {{ dealCon(formData.projectCode) }}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item label="需求发起人">
                  {{ dealCon(formData.userInfo) }}
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="联系方式">
                  {{ dealCon(formData.userPhone) }}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item label="支撑方式">
                  {{ dealMethod(formData.supportMehod) }}
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="支撑时限">
                  {{ dealCon(formData.time) }}
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <p v-if="hasReStar" class="group_title weight500 font_14 font_00060e">
            <span class="icon"></span>售前调度流程
          </p>
          <div v-for="(moduleItem, moduleIndex) in formData.moduleForm" :key="moduleItem.uid">
            <div class="module_title">
              <p class="weight500 font_14 font_00060e" style="margin-bottom:0">支撑需求{{ toChinese(moduleIndex + 1) }}</p>
              <!-- <el-button v-if="formData.moduleForm.length > 1 && moduleItem.status == 'reStar'" link type="danger"
                size="small" htmlType="button" @click="handleDeleteModule(moduleIndex)">删除模块</el-button> -->
            </div>
            <div class="module_group">
              <a-row v-if="moduleItem.status == 'reStar'">
                <a-col :span="24">
                  <a-form-item label="支撑需求描述：" :name="['moduleForm', moduleIndex, 'projectContent']"
                    :rules="[{ required: moduleIndex == 0 ? true : false, message: '请输入支撑需求描述', trigger: 'blur' }]">
                    <a-textarea :rows="7" :showCount="true" :maxlength="500"
                      placeholder="包含项目地点，具体需要支撑的内容，内容中切勿透露完整的项目名称和客户基本信息等，防止商机泄露。限制500个字以内"
                      style="border-radius: 8px;margin-bottom:20px;"
                      v-model:value="moduleItem.projectContent"></a-textarea>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-else>
                <a-col :span="24">
                  <a-form-item label="支撑需求描述：">
                    {{ moduleItem.projectContent || '-' }}
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item v-if="moduleItem.status == 'reStar'" label="附件：">
                <file-upload :customClassName="'custom_btn active_btn'" :fileInfo="{
                  type: 'other',
                  fileList: moduleItem.fileList,
                  index: null,
                  subIndex: null,
                  accept: '.doc,.docx,.ppt,.pptx',
                  required: false,
                  category: '2',
                  multiple: true,
                  acceptLength: 1,
                  size: 150,
                  mark: '仅支持ppt,doc,docx格式的文件上传(文件个数限1个,单个文件限150M）',
                }" @update-file="(fileInfo)=>setFileData(fileInfo,moduleItem)" @update-load="viewFileData"></file-upload>
              </a-form-item>
              <a-row v-else>
                <a-col :span="24">
                  <a-form-item label="附件">
                    <div class="file-list" v-if="moduleItem.fileList.length != 0">
                      <div class="flex">
                        <p>
                          <span>
                            <i class="iconfont icon-annex"></i>
                            <span>
                              &nbsp;{{ moduleItem.fileList[0]?.name }} &nbsp;</span>
                          </span>
                        </p>
                        <div class="font_0c70eb">
                          <span @click="view(moduleItem.fileList[0])">
                            &nbsp;预览</span>
                          <span @click="download(moduleItem.fileList[0])">
                            &nbsp;下载</span>
                        </div>
                      </div>
                    </div>
                    <div v-else>暂无附件</div>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item v-if="moduleItem.status == 'reStar'" label="业务模块选择：">
                <a-radio-group v-model:value="moduleItem.businessModuleType"
                  @change="(val) => changeBusinessModuleType(val, moduleIndex)">
                  <a-radio value="1">基础方案</a-radio>
                  <a-radio value="2">场景方案</a-radio>
                  <a-radio value="3">原子能力</a-radio>
                </a-radio-group>
              </a-form-item>
              <div>
                <p style="color: rgba(0, 0, 0, 0.85);font-size: 14px;font-weight: 500;">{{
                  businessModuleTypeComputed(moduleItem.businessModuleType) }}信息：</p>
                <div class="abInfo no-margin-left-right">
                  <div class="top">
                    <span style="width:107px;display: inline-block;text-align: right;"
                      class="weight500 font_14 font_00060e">
                      {{ businessModuleTypeComputed(moduleItem.businessModuleType) }}名称：</span>
                    <template v-if="moduleItem.status == 'reStar'">
                      <a-input disabled v-model:value="moduleItem.moduleName"
                        :placeholder="'请选择' + businessModuleTypeComputed(moduleItem.businessModuleType)"
                        style="width: 248px" allowClear></a-input>
                      <a-button style="margin-left: 10px;" class="custom_btn active_btn"
                        @click="() => showModuleTypeDialog(moduleIndex)">选择</a-button>
                    </template>
                    <span v-else>
                      {{ moduleItem.name || '-' }}
                    </span>
                  </div>
                  <div class="center">
                    <span style="width:107px;margin-top: 24px;text-align: right;vertical-align: top;"
                      class="weight500 font_14 font_00060e">
                      {{ businessModuleTypeComputed(moduleItem.businessModuleType) }}简介：
                    </span>
                    <p v-if="moduleItem.status == 'reStar' || moduleItem.status == 'submitPage'"
                      style="margin-left: 4px;margin-top: 24px; width: calc(100% - 120px); white-space: pre-wrap;">{{
                        moduleItem.intro }}</p>
                  </div>
                </div>
              </div>
              <template
                v-if="moduleItem.textList && moduleItem.textList.length > 0 && moduleItem.status == 'writeScore'">
                <a-descriptions v-for="(textItem, index) in moduleItem.textList" :key="index" bordered size="small"
                  :column='2' style="margin-bottom:20px;">
                  <a-descriptions-item label="生态能力方" :span="2">
                    {{ textItem && textItem.company }}&nbsp;&nbsp;{{ textItem && textItem.contactUserName
                    }}&nbsp;&nbsp;{{
                      textItem && textItem.contactPhone }}</a-descriptions-item>
                  <a-descriptions-item label="是否支撑">{{ textItem &&
                    textItem.dealType == '1' ? '同意' : textItem.dealType == '2' ? '拒绝' : ''
                  }}</a-descriptions-item>
                  <a-descriptions-item v-if="textItem && textItem.dealType == '1'" label="支撑人天">{{
                    textItem && textItem.allParseData.supportCos
                  }}</a-descriptions-item>
                  <a-descriptions-item v-if="textItem && textItem.dealType == '1'" label="实际支撑结果">{{
                    textItem && textItem.allParseData.suggest }}</a-descriptions-item>
                  <a-descriptions-item label="回复时间">{{ textItem && textItem
                    &&
                    textItem.dealTime }}</a-descriptions-item>
                  <a-descriptions-item
                    v-if="textItem && textItem.dealType == '1' && textItem.allParseData.fileListDeal.length > 0"
                    label="附件" :span="2">
                    <div class="file-list" v-if="textItem.allParseData.fileListDeal.length != 0">
                      <div class="flex">
                        <p>
                          <span>
                            <i class="iconfont icon-annex"></i>
                            <span>
                              &nbsp;{{ textItem.allParseData.fileListDeal[0]?.name }} &nbsp;</span>
                          </span>
                        </p>
                        <div class="font_0c70eb">
                          <span @click="view(textItem.allParseData.fileListDeal[0])">
                            &nbsp;预览</span>
                          <span @click="download(textItem.allParseData.fileListDeal[0])">
                            &nbsp;下载</span>
                        </div>
                      </div>
                    </div>
                  </a-descriptions-item>
                  <!-- enterpriseId为null是自有能力方或自有联系人 -->
                  <a-descriptions-item v-if="textItem.enterpriseId && textItem && textItem.dealType">
                    <template #label>
                      <div style="display: flex; ">
                        <span>支撑满意度</span>
                        <el-tooltip effect="dark" content="请对售前支撑整体满意度打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                          placement="top">
                          <div class="header-help-icon">
                            <el-icon :size="12">
                              <QuestionFilled />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </div>
                    </template>
                    <template v-if="textItem && textItem.dealType == '1'">
                      <el-input-number v-if="moduleItem.status == 'writeScore' && textItem && textItem.dealType == '1'"
                        v-model="textItem.satisfiedScore" :min="0" :max="10" :step="1">
                      </el-input-number>
                      <span v-else>{{ textItem &&
                        textItem
                        &&
                        textItem.satisfiedScore
                      }}</span>
                    </template>
                  </a-descriptions-item>
                  <!-- enterpriseId为null是自有能力方或自有联系人 -->
                  <a-descriptions-item v-if="textItem.enterpriseId && textItem && textItem.dealType">
                    <template #label>
                      <div style="display: flex; ">
                        <span>售前响应及时率</span>
                        <el-tooltip effect="dark" content="请对售前支撑响应及时率的满意度情况打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                          placement="top">
                          <div class="header-help-icon">
                            <el-icon :size="12">
                              <QuestionFilled />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </div>
                    </template>
                    <template v-if="textItem && textItem.dealType == '1'">
                      <el-input-number v-if="moduleItem.status == 'writeScore' && textItem && textItem.dealType == '1'"
                        v-model="textItem.responseScore" :min="0" :max="10" :step="1">
                      </el-input-number>
                      <span v-else>{{ textItem &&
                        textItem
                        &&
                        textItem.responseScore
                      }}</span>
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item v-if="textItem && textItem.dealType" label="生态评价" :span="2">
                    <el-input v-if="moduleItem.status == 'writeScore' && textItem && textItem.dealType == '1'"
                      type="textarea" v-model="textItem.suggest" placeholder="请输入生态评价" :rows="4" :cols="50"
                      :maxlength="100" :show-word-limit="true"></el-input>
                    <span v-else>{{ textItem &&
                      textItem &&
                      textItem.comment
                    }}</span>
                  </a-descriptions-item>
                </a-descriptions>
              </template>
              <el-table v-if="moduleItem.status == 'reStar' && moduleItem.editDataCompany?.[0]?.company?.length > 0"
                :data="moduleItem.editDataCompany" class="resize-table-header-line" :empty-text="'暂无数据'"
                :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
                style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                <el-table-column v-if="!moduleItem.ecologyType" prop="company" label="生态厂商" />
                <el-table-column v-if="moduleItem.ecologyType && moduleItem.ecologyType.includes('2')" prop="company"
                  label="生态厂商">
                  <template #default="scope">
                    <div class="table-row-wrapper" style="overflow-x: auto;">
                      <div v-for="(companyItem, index) in scope.row.company" :key="index" class="box person-wrap">
                        <div style="display: flex;width:280px;">
                          <a-radio-group :value="moduleItem.selectId" style="display: inline-block; ">
                            <a-radio :value="companyItem.contactPhone"
                              @change="(e) => onCheckChange(e, companyItem, moduleIndex)"
                              :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)">
                            </a-radio>
                          </a-radio-group>
                          <span v-if="companyItem.ecopartnerName" class="company_left"
                            style="display: flex;flex-direction: column;" @click="toCompanyDetail(companyItem)">
                            <span class="company_underline">{{ companyItem.ecopartnerName }}</span>
                            <span v-if="companyItem.auth == 0"
                              style="color: red;">该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！</span>
                            <span v-else-if="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve)"
                              style="color: red;">生态厂商暂无该生态联系人！</span>
                          </span>
                        </div>
                        <div class="company_right" style="display: flex; align-items: center;">
                          <img width="20px" height="20px" style="margin-bottom: 0;" src="@/assets/images/score.png" />
                          <span>
                            <span>生态评分：</span>
                            <span style="color: #FF9C39FF;font-weight: bold;">{{ companyItem.totalScore || '-'
                            }}</span>
                          </span>
                        </div>
                        <a-select v-model:value="companyItem.contactName"
                          @change="(value) => selectUserCom(value, companyItem)"
                          :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1) || rejectCompanyIdlist.some((value) => value.userId == companyItem.userId)">
                          <template v-for="(opt, index) in companyItem.contactList" :key="index">
                            <a-select-option :value="opt.contactName" :disabled="opt.approve != 1">
                              {{ opt.contactName }}
                            </a-select-option>
                          </template>
                        </a-select>
                        <span
                          :style="{ color: companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1 ? '' : '#999' }">
                          {{ companyItem.contactPhone }}
                        </span>
                      </div>
                    </div>
                    <a-button class="custom_btn active_btn margin_top_12 margin_bottom_2" type="primary"
                      @click="addCooperate(scope.row, moduleIndex)">新增生态厂商</a-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-table v-if="moduleItem.status == 'reStar' && moduleItem.editDataCompany?.[0]?.ownPerson?.length > 0"
                :data="moduleItem.editDataCompany" class="resize-table-header-line" :empty-text="'暂无数据'"
                :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
                style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                <el-table-column v-if="!moduleItem.ecologyType" prop="ownPerson" label="自有能力方" />
                <el-table-column v-if="moduleItem.ecologyType && moduleItem.ecologyType.includes('1')" prop="ownPerson"
                  label="自有能力方">
                  <template #default="scope">
                    <div v-for="(ownPersonItem, index) in scope.row.ownPerson" :key="index" class="box person-wrap">
                      <div style="display: flex;width:200px;">
                        <a-radio-group :value="moduleItem.selectIdOwn">
                          <a-radio :value="ownPersonItem.contactPhone"
                            @change="(e) => onCheckChange(e, ownPersonItem, moduleIndex)">
                          </a-radio>
                        </a-radio-group>
                        <span>
                          {{ ownPersonItem.belong }}
                        </span>
                      </div>
                      <span class="contactName">
                        {{ ownPersonItem.contactName }}
                      </span>
                      <p class="contactPhone">
                        {{ ownPersonItem.contactPhone }}
                      </p>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <p class="group_title weight500 font_14 font_00060e">模块流程节点：
                <span style="font-weight:normal;">{{ moduleItem.nodeName }}</span>
              </p>
              <p v-if="moduleItem.status == 'reStar'" class="group_title weight500 font_14 font_00060e"
                style="margin-top:20px">驳回理由：
                <span style="font-weight:normal;color:red;">{{ moduleItem.auditReason }}</span>
              </p>
              <div class="flex just-center margin_t_32 margin_b_24">
                <a-button v-if="moduleItem.status == 'reStar' || moduleItem.status == 'submitPage'"
                  class="margin_r_10 custom_btn cancel_btn" @click="endWork(moduleIndex)">结束</a-button>
                <a-button class="margin_r_10 custom_btn active_btn" @click="reSubmit(moduleIndex)" :loading="addLoading"
                  v-if="moduleItem.status == 'reStar'">重新提交</a-button>
                <a-button v-if="moduleItem.status != 'reStar' && moduleItem.status != 'submitPage'"
                  class="custom_btn active_btn" @click="submit(moduleIndex)" :loading="addLoading">确定</a-button>
              </div>
            </div>
          </div>
          <!-- <div v-if="hasReStar" style="width: 100%;text-align: center;margin: 20px 0 0 0;">
            <a-button class="custom_btn active_btn" @click="addModule">新增支撑模块</a-button>
          </div> -->
          <a-row style="padding-left: 42px;margin-top:20px" v-if="hasReStar">
            <a-col :span="9">
              <a-form-item label="调度管理员" name="dispatchUser">
                <a-select placeholder="请选择调度管理员" v-model:value="formData.dispatchUser" allowClear>
                  <template v-for="(opt, index) in personList" :key="index">
                    <a-select-option :value="opt.id">
                      {{ formatRealName(opt.realName) }}
                    </a-select-option>
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <p class="group_title weight500 font_14 font_00060e">
            <span class="icon"></span>工单流程
          </p>
          <div style="padding: 0 24px">
            <el-table :data="tableDataWork" class="resize-table-header-line" :empty-text="'暂无数据'" border
              :header-cell-style="{ textAlign: 'center' }" style="width: 100%; margin-top: 20px">
              <el-table-column align="center" prop="activityName" label="工单流程" width="180" />
              <el-table-column align="center" prop="assigneeName" label="处理人">
                <template #default="scope">
                  {{ scope.row.assigneeName }}
                  {{ scope.row.orgName ? '(' + scope.row.orgName + ')' : '' }}
                  {{ scope.row.phone }}
                </template>
              </el-table-column>
              <el-table-column align="left" prop="dealContent" label="处理内容">
                <template #default="scope">
                  <el-tooltip trigger="hover" popper-class="custom-tooltip" :content="scope.row.dealContent"
                    placement="top" v-if="
                      scope.row.dealContent && scope.row.dealContent.length > 60
                    ">
                    <p v-if="scope.row.dealContent !== ''" class="content_control">
                      {{ scope.row.dealContent }}
                    </p>
                  </el-tooltip>
                  <p v-if="scope.row.dealContent == ''">-</p>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="endTime" label="处理时间" />
            </el-table>
          </div>
        </a-form>
      </div>
      <div class="flex just-center margin_t_32 margin_b_24">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="cancel">返回</a-button>
      </div>
    </div>
    <a-modal v-model:visible="showAdd" title="生态厂商新增" @ok="handleOk" :footer="null" @close="closeAdd" width="50%">
      <a-form ref="addFormRef" :model="CooperateData" labelAlign="right" :rules="addRules">
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态合作方" name="company">
              <a-select placeholder="请选择生态合作方" v-model:value="CooperateData.company" allowClear show-search
                @select="ecologyChangeOld" @search="handleSearch" :not-found-content="fetching ? undefined : null"
                :filter-option="false" :virtual-scroll="{
                  itemHeight: 32,
                  height: 400,
                  remain: 8,
                }">
                <template v-for="opt in displayOptions" :key="opt.name">
                  <a-select-option :value="opt.name" :disabled="companyIdList?.includes(opt.enterpriseId)">
                    {{ opt.name }}
                  </a-select-option>
                </template>
              </a-select>
              <div v-if="CooperateData.company">
                <p style="color: red; margin-bottom: 0; margin-left: 12px"
                  v-if="CooperateData.sync != 1 || CooperateData.auth != 1">
                  该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！
                </p>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态联系人" name="contanct">
              <a-select placeholder="请选择生态联系人" v-model:value="CooperateData.contanct" allowClear
                @change="(value) => selectUser(value, item)">
                <template v-for="(opt, index) in contanctList" :key="index">
                  <a-select-option :value="opt.contactName" :disabled="opt.approve != 1 ||
                    rejectCompanyIdlist.some(
                      (value) => value.ecopartnerName == opt.belong
                    )
                    ">
                    {{ opt.contactName }}
                  </a-select-option>
                </template>
              </a-select>
            </a-form-item></a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态联系方式：">
              <a-input disabled :value="CooperateData.phone" placeholder="请输入生态联系方式">
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="负责区域：">
              <a-input disabled :value="CooperateData.area" placeholder="请选择负责区域">
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex just-center margin_t_32">
        <a-button class="margin_r_10" @click="closeAdd">取消</a-button>
        <a-button type="primary" @click="submitAdd" :loading="addLoading" :disabled="isSyncAuth">提交</a-button>
      </div>
    </a-modal>
    <a-modal v-model:visible="showScore" title="提示" @ok="handleOkScore" :footer="null" width="40%">
      <a-form ref="scoreFormRef" :model="formDataScore" labelAlign="right" :rules="rulesScore">
        <a-row>
          <a-col :span="24">
            <a-form-item label="是否转售中" name="isTurn">
              <a-radio-group v-model:value="formDataScore.isTurn" @change="changeRadio">
                <a-radio value="1">是</a-radio>
                <a-radio value="2">否</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="isSubmit">
          <a-col :span="24">
            <a-form-item label="项目名称" name="projectName">
              <a-input v-model:value="formDataScore.projectName" placeholder="请填写DICT项目管理系统项目名称"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="isSubmit">
          <a-col :span="24">
            <a-form-item label="项目编码" name="projectCode">
              <a-input v-model:value="formDataScore.projectCode" type="number"
                placeholder="请填写DICT项目管理系统项目编码"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="isSubmit">
          <a-col :span="24">
            <a-form-item label="交付经理" name="deliveryManager">
              <a-select placeholder="请选择交付经理" v-model:value="formDataScore.deliveryManager" allowClear show-search
                :filter-option="filterOption">
                <template v-for="(opt, index) in personListNew" :key="index">
                  <a-select-option :value="String(opt.id)" :label="opt.realName">
                    {{ opt.realName }}
                  </a-select-option>
                </template>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div style="display: flex; padding-left: 65px; color: #a2abb5">
        <p>备注：</p>
        <p>选择是将发送短信给交付经理</p>
      </div>
      <div style="width: 100%; text-align: center; margin-top: 24px">
        <a-button class="margin_r_10" @click="handleCloseScore"> 取消 </a-button>
        <a-button type="primary" @click="handleOkScore"> 确认 </a-button>
      </div>
    </a-modal>
    <a-modal :visible="moduleTypeVisible" @cancel="closeModal"
      :title="'选择' + businessModuleTypeComputed(formData.moduleForm[currentModuleIndex]?.businessModuleType)"
      width="1200px" centered :destroyOnClose="true" :maskClosable="false" :footer="null">
      <guide-table :sourceType="formData.moduleForm[currentModuleIndex]?.businessModuleType" @close="closeModal"
        @ok="(val) => handleSelectModule(val, currentModuleIndex)"></guide-table>
    </a-modal>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { message } from "ant-design-vue";
import { getPriviligesData } from "@/api/processManage/permissionConfig/index.js";
import { getAllUserList } from "@/api/system/user";
import { QuestionFilled } from "@element-plus/icons-vue"; // 添加引入QuestionFilled图标
import {
  submitWithdrawProcess,
  batchSubmitWithdrawProcess,
  cityBack,
  midDispatch
} from "@/api/processManage/backlog&completed/index.js";
import {
  getProcessInfo,
  getProcessInfoNew,
  selectOrderById,
  operateOrderById,
  querytOrderById,
} from "@/api/processManage/index.js";
import { selectTree, selectTreeNew } from "@/api/system/team";
import dayjs from "dayjs";
import GuideTable from "./components/guideTable.vue";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import { sortTextList, processDuplicateCompaniesByName } from '@/utils/index.js';
export default defineComponent({
  components: { QuestionFilled, GuideTable },
  setup() {
    const Router = useRouter();
    const Route = useRoute();
    const getUserInfo = JSON.parse(window.localStorage.getItem("userInfo"));
    const data = reactive({
      hasReStar: false,
      moduleTypeVisible: false,
      currentModuleIndex: 0,
      operateIndex: 0, // 待操作的模块索引
      contralSupport: false, //省直调度人
      formData: {
        projectName: "",
        title: "", // 工单标题
        projectCode: "",
        dispatchUser: undefined,
        userPhone: "",
        userInfo: "",
        supportMehod: "",
        time: "",
        moduleForm: [{
          uid: null,
          procInstId: "",
          taskId: "",
          // editFinishData: [], // 对生态厂商的评分
          // reSelectPage: false,// 是否重新选择厂商
          textList: [], // 生态厂商列表
          ownProvince: [], //自有联系人list
          tableData1: [],
          projectContent: "",
          fileList: [],
          businessModuleType: "1",
          moduleName: "",
          intro: "",
          selectUsers: [],
          ecologyType: "",
          editDataCompany: [], // 模块可选择生态方列表
          selectCompanyList: {},
          ipartnerId: "",
          selectId: "",
          selectIdOwn: "",
          selectPhone: "",
          needProvince: false, // 是否申请省级调度支撑
          isBySelf: "2", // 1自己支撑2不自己支撑
        }]
      },
      rejectCompanyIdlist: [], //厂商拒绝后调度人页面重新选择厂商数据
      ownPersonPhone: [],
      personListNew: [],
      formDataScore: {
        isTurn: "2",
        projectName: "",
        projectCode: "",
        deliveryManager: "",
      },
      personList: [],
      showScore: false,
      isSubmit: false,
      baseData: {
        name: "",
        intro: "",
      },
      addLoading: false,
      userInfo: getUserInfo,
      rules: {
        title: [{ required: true, message: "请输入工单标题", trigger: "blur" }],
        projectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        // projectCode: [{ required: false, message: "项目编码不能为空" }],
        projectContent: [
          { required: true, message: "请输入项目描述", trigger: "blur" },
        ],
        userInfo: [
          { required: true, message: "请输入需求发起人", trigger: "blur" },
        ],
        userPhone: [
          { required: true, message: "请输入需求发起人", trigger: "blur" },
        ],
        time: [{ required: true, message: "请选择支撑时限", trigger: "change" }],
        supportMehod: [
          { required: true, message: "请选择支撑方式", trigger: "change" },
        ],
        dispatchUser: [
          { type: 'number', required: true, message: "请选择调度管理员", trigger: "change" },
        ],
      },
      addRules: {
        company: [
          {
            required: true,
            message: "请选择生态合作方",
            trigger: "change",
          },
        ],
        contanct: [
          {
            required: true,
            message: "请选择联系人",
            trigger: "change",
          },
        ],
      },
      rulesScore: {
        isTurn: [
          {
            required: true,
            message: "请选择是否流转",
            trigger: "change",
          },
        ],
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        projectCode: [{ required: true, message: "项目编码不能为空" }],
        deliveryManager: [
          {
            required: true,
            message: "请选择交付负责人",
            trigger: "change",
          },
        ],
      },
      viewLoading: false,
      formLoading: false,
      tableDataWork: [],
      showAdd: false,
      dataCompany: [],
      // selectId: null,
      // selectIdOwn: null,
      CooperateData: {
        company: undefined,
        contanct: undefined,
        phone: undefined,
        area: undefined,
        enterpriseId: undefined,
        ecopartnerId: undefined,
        approve: undefined,
        totalScore: undefined
      },
      contanctList: [],
      isSyncAuth: false,
      formName: "",
      linkId: "",
      companyIdList: [],
      teamOldList: [],
      displayOptions: [],
      fetching: false,
      // auditReason: "",
    });
    const scoreFormRef = ref(null);
    const mainFormRef = ref(null);
    const addFormRef = ref(null);
    const toChinese = (num) => {
      const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
      if (num <= 10) return chineseNumbers[num];
      if (num < 20) return `十${chineseNumbers[num - 10]}`;
      return num.toString();
    }
    const businessModuleTypeComputed = (type) => {
      if (type == 1) return '方案';
      if (type == 2) return '场景方案';
      if (type == 3) return '能力';
      return '';
    };

    const getData = async () => {
      try {
        data.formLoading = true;
        getAllUserList({ roleId: 71, scope: 0 }).then((res) => {
          // 转售中时的交付经理列表
          data.personListNew = res.data;
        });
        const apiCall = Route.query.type === "dealedAll" ? selectOrderById : operateOrderById;
        apiCall(Route.query.orderId).then((res) => {
          const tempParsedData = JSON.parse(JSON.parse(res.data.procInstList[0].customDataForm));
          if (res.data.procInstList && res.data.procInstList.length > 0) {
            data.formData = { ...data.formData, ...tempParsedData }; // 填充工单信息和基础信息
            for (let index = 0; index < res.data.procInstList.length; index++) {
              // 遍历并回填模块及生态方信息
              const element = res.data.procInstList[index];
              let parsedData = JSON.parse(JSON.parse(element.customDataForm));
              if (!data.formData.moduleForm[index]) {
                data.formData.moduleForm.push({
                  uid: null,
                  procInstId: "",
                  // editFinishData: [], // 对生态厂商的评分
                  textList: [], // 生态厂商列表
                  ownProvince: [], //自有联系人list
                  tableData1: [],
                  projectContent: "",
                  fileList: [],
                  businessModuleType: "1",
                  moduleName: "",
                  intro: "",
                  selectUsers: [],
                  ecologyType: "",
                  editDataCompany: [],
                  selectCompanyList: {},
                  ipartnerId: "",
                  selectId: "",
                  selectIdOwn: "",
                  selectPhone: "",
                  needProvince: false, // 是否申请省级调度支撑
                  isBySelf: "2", // 1自己支撑2不自己支撑
                });
              }
              const { ownPerson = [], ownProvince = [] } = parsedData || {};
              const [personPhone, provincePhone] = [ownPerson[0]?.contactPhone, ownProvince[0]?.contactPhone];
              data.ownPersonPhone = [personPhone, provincePhone].filter(Boolean);
              data.isOwnModule = data.ownPersonPhone.includes(data.userInfo?.phone);
              data.formData.moduleForm[index] = { ...data.formData.moduleForm[index], ...parsedData };
              if (Route.query.type == "multiModule") {
                const taskName = element?.tasks?.[0]?.taskName;
                if (taskName == "提交申请") {
                  if (element?.tasks?.[0].procVars.selfSupport) {
                    data.formData.moduleForm[index].status = "submitPage";
                  } else {
                    data.formData.moduleForm[index].status = "reStar";
                  }
                }
                if (taskName == "生态评分") {
                  data.formData.moduleForm[index].status = "writeScore";
                }
              }
              data.formData.moduleForm[index].taskId = element?.tasks?.[0]?.taskId || element?.tasks?.[0]?.id || "";
              data.formData.moduleForm[index].procInstId = element.procInstId;
              data.formData.moduleForm[index].uid = element.linkId;
              data.formData.moduleForm[index].selectCompanyList = parsedData.selectCompanyList || {};
              data.formData.moduleForm[index].editDataCompany = (parsedData.company?.length > 0 && parsedData.company[0].name) ? parsedData.company : [{
                name: parsedData.name,
                company: parsedData.company,
                ownPerson: parsedData.ownPerson || [],
              },];
              if (parsedData.ownProvince) {
                data.formData.moduleForm[index].ownProvince = parsedData.ownProvince;
              }
              data.formData.moduleForm[index].projectContent = parsedData.projectContent;
              data.formData.moduleForm[index].businessModuleType = parsedData.contentType + "";
              data.formData.moduleForm[index].moduleName = parsedData.name
              data.formData.moduleForm[index].nodeName = element.historyProcNodeList[0].activityName;
              data.formData.moduleForm[index].fileList = parsedData.fileList || [];
              data.formData.moduleForm[index].selectId = parsedData.selectPhone;
              data.formData.moduleForm[index].selectIdOwn = parsedData.selectIdOwn;
              if (data.formData.moduleForm[index].status == 'reStar') {
                actioAuditReason(element.historyProcNodeList, parsedData, index);
              }
              queryEcoparterComment(element?.preSaleDispatchFeedbackInfos, index, parsedData);
            }
            // }
            // 处理时间格式
            // 修改后的日期处理逻辑
            if (tempParsedData.time) {
              try {
                // 如果 tempParsedData.time 是字符串，按格式解析
                if (typeof tempParsedData.time === "string") {
                  data.formData.time = dayjs(tempParsedData.time, "YYYY-M-D");
                }
                // 如果已经是 Day.js 对象，直接赋值
                else if (dayjs.isDayjs(tempParsedData.time)) {
                  data.formData.time = tempParsedData.time;
                }
                // 否则设为 null
                else {
                  data.formData.time = null;
                }
                // 验证日期是否有效
                if (!data.formData.time?.isValid()) {
                  data.formData.time = null;
                }
              } catch (e) {
                console.error("日期解析错误:", e);
                data.formData.time = null;
              }
            } else {
              data.formData.time = null;
            }
            // 设置基础字段
            data.formData = { ...data.formData, ...tempParsedData };
            data.baseData = { ...data.baseData, ...tempParsedData };
            // 设置表单名称
            const formNameMap = { 1: "方案", 2: "场景", 3: "能力" };
            data.formName = formNameMap[tempParsedData.contentType] || tempParsedData.formName;
            // 构建公司数据
            data.dataCompany = [
              {
                name: tempParsedData.name,
                company: tempParsedData.company,
                ownPerson: tempParsedData.ownPerson,
              },
            ];
            data.companyIdList = tempParsedData.companyIdList;
            data.linkId = tempParsedData.linkId;
          }
          // 处理历史记录
          let tableDataWork = res.data.historyProcNodeList;
          if (tableDataWork[tableDataWork.length - 1].activityName === "已审核") {
            tableDataWork = tableDataWork.slice(1, -1);
          }
          if (tableDataWork[tableDataWork.length - 1].activityName === "结束") {
            tableDataWork = tableDataWork.slice(0, -1);
          }
          data.tableDataWork = tableDataWork.sort((a, b) => {
            // 按endTime升序排列,null值排在最后，endTime为空是待办项
            if (!a.endTime) return 1;
            if (!b.endTime) return -1;
            return new Date(a.endTime) - new Date(b.endTime);
          }).map((item) => {
            const result = {
              activityName: item.activityName,
              assigneeName: item.assigneeName,
              phone: item.phone,
              dealContent: "",
              endTime: item.endTime || "-",
              orgName: item.orgName,
            };
            if (item.commentList?.length > 0) {
              const comment = item.commentList[0];
              result.dealContent = comment.fullMessage;
              if (item.activityName == "能力方反馈") {
                if (item.commentList[0].type == "2") {
                  item.dealContent = "驳回。" + item.commentList[0].message;
                }
              }
            }
            return result;
          });
          data.hasReStar = data.formData.moduleForm.some(item => item.status == 'reStar')
          data.formData.dispatchUser = Number(tempParsedData.dispatchProvinceUser) || Number(tempParsedData.dispatchUser);
          // 获取人员列表
          getPersonList();

        })
      } catch (error) {
        console.error("数据获取失败:", error);
      } finally {
        data.formLoading = false;
      }
    };
    const actioAuditReason = (nodeList, parsedData, index) => {
      let tableDataWork = JSON.parse(JSON.stringify(nodeList));
      if (tableDataWork[tableDataWork.length - 1].activityName === "已审核") {
        tableDataWork = tableDataWork.slice(1, -1);
      } else {
        tableDataWork = tableDataWork.slice(1);
      }
      if (tableDataWork[tableDataWork.length - 1].activityName === "结束") {
        tableDataWork = tableDataWork.slice(0, -1);
      }
      const distDataList = tableDataWork
        .reverse()
        .slice(1)
        .map((item) => {
          const result = {
            activityName: item.activityName,
            assigneeName: item.assigneeName,
            phone: item.phone,
            dealContent: "",
            endTime: item.endTime || "-",
          };
          if (item.commentList?.length > 0) {
            const comment = item.commentList[0];
            result.dealContent = comment.fullMessage;
            if (item.activityName == "能力方反馈") {
              if (item.commentList[0].type == "2") {
                item.dealContent = "驳回。" + item.commentList[0].message;
              }
            }
          }
          data.selectId = parsedData.selectPhone;
          data.selectIdOwn = parsedData.selectIdOwn;
          return result;
        });
      data.formData.moduleForm[index].auditReason = distDataList[distDataList.length - 1]?.dealContent;
    };
    const getPersonList = () => {
      let roleKey = "";
      let org = data.userInfo.orgId;
      if (
        org == "2" ||
        org == "3" ||
        org == "4" ||
        org == "5" ||
        org == "6" ||
        org == "7" ||
        org == "8" ||
        org == "9" ||
        org == "10" ||
        org == "11" ||
        org == "12" ||
        org == "13" ||
        org == "14"
      ) {
        roleKey = "cityIndustryCharge";
        data.isProvinceUser = false;
      } else {
        roleKey = "provinceDispatchAdmin";
        data.isProvinceUser = true;
      }
      if (roleKey == "cityIndustryCharge") {
        getAllUserList({ roleKey }).then((res) => {
          const promises = [Promise.resolve(res)];
          Promise.all(promises).then((results) => {
            data.personList = results.flatMap((result) => result.data);
          });
          getPriviligesData({ roleIds: 72, pageSize: 100 }).then((res) => {
            data.personList = [...data.personList, ...res.data.rows];
          });
        });
      } else {
        getAllUserList({ roleKey, scope: 0 }).then((res) => {
          const promises = [Promise.resolve(res)];
          Promise.all(promises).then((results) => {
            data.personList = results.flatMap((result) => result.data);
          });
        });
      }
    };

    const reSubmit = async (index) => {
      try {
        data.addLoading = true;
        await mainFormRef.value?.validate();
        if (data.formData.projectCode) {
          if (data.formData.projectCode.includes("-")) {
            message.warning("项目编号应为纯数字");
            data.addLoading = false;
            return;
          }
        }
        data.formData.time = dayjs(data.formData.time).format("YYYY-M-D");
        const operateModule = data.formData.moduleForm[index];
        if (data.rejectCompanyIdlist.some((value) => value.userId == operateModule.selectCompanyList.userId)) {
          message.warning("该厂商联系人已拒绝，请重新选择");
          data.addLoading = false;
          return
        }
        operateModule.projectName = data.formData.projectName;
        operateModule.projectCode = data.formData.projectCode;
        operateModule.dispatchUser = data.formData.dispatchUser;
        operateModule.userPhone = data.formData.userPhone;
        operateModule.userInfo = data.formData.userInfo;
        operateModule.supportMehod = data.formData.supportMehod;
        operateModule.time = data.formData.time;
        const { editDataCompany, ...moduleFormWithoutEcopartner } = operateModule;// 移除moduleForm的ecopartnerList, editDataCompany
        const postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "重新提交",
          nextUserIds: data.formData.dispatchUser,
          variables: {
            customDataForm: JSON.stringify(moduleFormWithoutEcopartner),
            nextUserIds: data.formData.dispatchUser,
            isProvince: data.isProvinceUser,
          },
        }];
        batchSubmitWithdrawProcess(postData).then((res) => {
          message.success("提交成功");
          handlExistTask();
          // data.addLoading = false;
          // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
        });
      } catch (error) {
        console.log(error);
        data.addLoading = false;
      }
    };

    const cancel = () => {
      window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog";
    };

    const addCooperate = (value, index) => {
      data.companyId = value.name;
      data.showAdd = true;
      data.currentModuleIndex = index;
    };

    const closeAdd = () => {
      data.showAdd = false;
      data.contanctList = [];
      data.CooperateData = {
        company: undefined,
        contanct: undefined,
        phone: undefined,
        area: undefined,
        approve: undefined,
        enterpriseId: undefined,
        ecopartnerId: undefined,
        totalScore: undefined,
      };
    };
    const fetchData = async () => {
      data.fetching = true;
      try {
        const response = await selectTree();
        const mockData = response.data.filter((item) => {
          return item.id != "1189";
        });
        data.teamOldList = mockData || [];
        data.displayOptions = mockData;
      } catch (error) {
        data.displayOptions = [];
      } finally {
        data.fetching = false;
      }
    };
    const handleSearch = (val) => {
      data.fetching = true;
      selectTree({ name: val }).then((res) => {
        data.fetching = false;
        data.teamOldList = res.data.filter((item) => item.id != "1189");
        data.displayOptions = data.teamOldList;
      });
    };

    const selectUser = (val) => {
      const result = data.contanctList.find((item) => item.contactName === val);
      if (result) {
        data.CooperateData.phone = result.contactPhone;
        data.CooperateData.area = result.contactAddress || "江苏省";
        data.CooperateData.approve = result.approve;
      } else {
        data.CooperateData.phone = null;
        data.CooperateData.area = "江苏省";
        data.CooperateData.approve = undefined;
      }
    };

    const selectUserCom = (value, item) => {
      item.contactName = item.contactList.find(
        (opt) => opt.userId === value || opt.contactName === value
      ).contactName;
      const selectedCompany = item.contactList.find(
        (opt) => opt.userId === value || opt.contactName === value
      );
      if (selectedCompany) {
        item.contactPhone = selectedCompany.contactPhone;
        item.userId = selectedCompany.userId;
      }
    };

    const onCheckChange = (e, item, index) => {
      const { value } = e.target;
      // data.selectId = item.ecopartnerName ? value : null;
      // data.selectIdOwn = item.ecopartnerName ? null : value;
      data.formData.moduleForm[index].selectCompanyList = item;
      data.formData.moduleForm[index].ipartnerId = item.userId;
      data.formData.moduleForm[index].selectId = value;
      data.formData.moduleForm[index].selectIdOwn = value;
      data.formData.moduleForm[index].selectPhone = value;
    };

    const submitAdd = async () => {
      try {
        await addFormRef.value?.validate();
        if (data.formData.moduleForm[data.currentModuleIndex].status == "reStar") {
          let com = {
            ecopartnerName: data.CooperateData.company,
            contactPhone: data.CooperateData.phone,
            contactName: data.CooperateData.contanct,
            userId: data.CooperateData.userId,
            contactList: data.contanctList,
            enterpriseId: data.CooperateData.enterpriseId,
            sync: data.CooperateData.sync,
            auth: data.CooperateData.auth,
            approve: data.CooperateData.approve,
            totalScore: data.CooperateData.totalScore,
          };
          data.formData.moduleForm[data.currentModuleIndex].editDataCompany[0].company.push(com);
          data.companyIdList = data.formData.moduleForm[data.currentModuleIndex].editDataCompany[0].company.map(
            (item) => item.enterpriseId
          );
        } else {
          const newCompany = {
            ecopartnerName: data.CooperateData.company,
            contactPhone: data.CooperateData.phone,
            contactName: data.CooperateData.contanct,
            contactList: data.contanctList,
            enterpriseId: data.CooperateData.enterpriseId,
            ecopartnerId: data.CooperateData.ecopartnerId,
            sync: data.CooperateData.sync,
            auth: data.CooperateData.auth,
            approve: data.CooperateData.approve,
            totalScore: data.CooperateData.totalScore,
          };

          data.dataCompany[0].company.push(newCompany);
          data.companyIdList = data.dataCompany[0].company.map(
            (item) => item.enterpriseId
          );
        }
        message.success("添加成功");
        closeAdd();
      } catch (error) {
        console.error(error);
      }
    };

    const toDetail = () => {
      let url = "";
      if (data.formName == "方案") {
        url = `${window.location.origin}/backend/#/solveNew/detailNew?id=${data.linkId}`;
      } else if (data.formName == "场景") {
        url = `${window.location.origin}/backend/#/solveNew/applyNew?id=${data.linkId}&activeBtn=2`;
      } else {
        url = `${window.location.origin}/backend/#/module/modulelNew?id=${data.linkId}`;
      }
      window.open(url);
    };

    const disabledDate = (current) => {
      return current && current < dayjs().startOf("day");
    };

    onMounted(() => {
      fetchData();
      getData();
    });
    const formatRealName = (realName) => {
      if (!realName) return "";
      realName = realName.replace(/级/g, "");
      if (realName.includes("王达伟")) {
        return "王达伟(省公司交通、融合创新行业支撑负责人)";
      }
      if (realName.includes("仲伟奇")) {
        return "仲伟奇(省公司农业文旅行业支撑负责人)";
      }
      if (realName.includes("孙晓星")) {
        return "孙晓星(省公司政企行业支撑负责人)";
      }
      if (realName.includes("魏宇珺")) {
        return "魏宇珺(省公司教育行业支撑负责人)";
      }
      if (realName.includes("丁德胜")) {
        return "丁德胜(省公司医疗行业支撑负责人)";
      }
      if (realName.includes("徐剑宏")) {
        return "徐剑宏(省公司政法公安行业支撑负责人)";
      }
      if (realName.includes("肖明")) {
        return "肖明(省公司党政行业支撑负责人)";
      }
      if (realName.includes("许文杰")) {
        return "许文杰(省公司金融、互联网行业支撑负责人)";
      }
      if (realName.includes("吴鹏")) {
        return "吴鹏(省公司工业行业支撑负责人)";
      }
      if (realName.includes("戴云平")) {
        return "戴云平(省直项目交付团队负责人)";
      }

      return realName;
    };
    const dealCon = (v) => {
      if (v) {
        return v;
      } else {
        return "-";
      }
    };
    const dealMethod = (v) => {
      if (v == "1") {
        return "远程支撑";
      } else if (v == "2") {
        return "现场支撑";
      } else {
        return "-";
      }
    };
    const endWork = (index) => {
      data.operateIndex = index;
      const operateModule = data.formData.moduleForm[data.operateIndex];
      if (operateModule.status == "reStar") {
        // 调度人驳回，发起人结束流程
        // 查看是否还有待办任务
        querytOrderById(Route.query.orderId).then((res) => {
          const scored = res.data.scored;
          const taskList = res.data.todoTasks || [];
          const procCount = res.data.procCount;
          if (procCount == 1) {
            // 单模块直接结束
            handlExistTask();
            return
          }
          if (taskList.length > 1) {
            // 不是最后一个结束的模块
            // 只提交同意的且没有评过分的生态方数据
            const postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "发起人结束调度",
            }];
            cityBack(postData).then((res) => {
              message.success(res.msg);
              handlExistTask();
              // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
            });
          } else {
            // 最后一个结束的模块，弹售中弹窗
            if (taskList.length == 1 && taskList[0].taskId == operateModule.taskId && scored) {
              // 最后一个结束的模块，弹售中弹窗
              data.formDataScore.projectCode = data.formData.projectCode;
              data.formDataScore.projectName = data.formData.projectName;
              data.formDataScore.deliveryManager = undefined;
              data.formDataScore.isTurn = "2";
              data.showScore = true;
              data.addLoading = false;
            } else {
              const postData = [{
                taskId: operateModule.taskId,
                procInsId: operateModule.procInstId,
                comment: "发起人结束调度",
              }];
              cityBack(postData).then((res) => {
                message.success(res.msg);
                handlExistTask();
                // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
              });
            }
          }
        })
      } else {
        // 调度人自己支撑
        data.formDataScore.projectCode = data.formData.projectCode;
        data.formDataScore.projectName = data.formData.projectName;
        data.formDataScore.deliveryManager = undefined;
        data.formDataScore.isTurn = "2";
        data.showScore = true;
      };
    };
    const handleOrderTask = () => {
      if (data.formDataScore.projectCode && !/^\d+$/.test(data.formDataScore.projectCode)) {
        message.warning("项目编号应为纯数字，且不能为负数或包含字母");
        data.addLoading = false;
        return;
      }
      const operateModule = data.formData.moduleForm[data.operateIndex];
      const ownPerson = operateModule.textList.filter((item) => !item.enterpriseId && (!item.scored && item.dealType == '1')); // enterpriseId为null是自有联系人或自有能力方，不需要打分，需要生态评价
      const thirdPerson = operateModule.textList.filter((item) => item.enterpriseId && (!item.scored && item.dealType == '1')); // enterpriseId非null是生态厂商，需要打分，需要生态评价
      // 移除allParseData
      const filteredOwnPerson = ownPerson.map(({ allParseData, ...rest }) => rest);
      const filteredThirdPerson = thirdPerson.map(({ allParseData, ...rest }) => rest);
      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: "评分完毕",
        variables: {
          addScoreInfo: JSON.stringify({ writeScore: ownPerson.length > 0 ? filteredOwnPerson : filteredThirdPerson }),
          midDispatchInfo: JSON.stringify(data.formDataScore),
          sendMsg: data.formDataScore.isTurn == "1" ? true : false,
        },
      }];
      batchSubmitWithdrawProcess(postData).then((res) => {
        operateOrderById(Route.query.orderId).then((res2) => {
          let tempProInstList = res2.data.procInstList;
          if (tempProInstList && tempProInstList.length > 0) {
            // 该工单仍有待办，操作完成
            message.success("操作完成");
            actionInitData();
          } else {
            message.success("评分完毕");
            window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
          }
        })
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const actionInitData = () => {
      data.formData.moduleForm = [{
        uid: null,
        procInstId: "",
        // editFinishData: [], // 对生态厂商的评分
        textList: [], // 生态厂商列表
        ownProvince: [], //自有联系人list
        tableData1: [],
        projectContent: "",
        fileList: [],
        businessModuleType: "1",
        moduleName: "",
        intro: "",
        selectUsers: [],
        ecologyType: "",
        editDataCompany: [],
        selectCompanyList: {},
        ipartnerId: "",
        selectId: "",
        selectIdOwn: "",
        selectPhone: "",
        needProvince: false, // 是否申请省级调度支撑
        isBySelf: "2", // 1自己支撑2不自己支撑
      }];
      getData();
    }
    const handleCloseScore = () => {
      data.showScore = false;
      data.formDataScore.deliveryManager = undefined;
      data.formDataScore.isTurn = "2";
      data.isSubmit = false;
    };
    // 评分完成后，是否转售中的弹窗确定事件
    const handleOkScore = async () => {
      await scoreFormRef.value?.validate();
      const operateModule = data.formData.moduleForm[data.operateIndex];
      if (operateModule.status === 'writeScore') {
        // 能力方支撑流程
        if (data.formDataScore.projectCode && !/^\d+$/.test(data.formDataScore.projectCode)) {
          message.warning("项目编号应为纯数字，且不能为负数或包含字母");
          data.addLoading = false;
          return;
        }
        const ownPerson = operateModule.textList.filter(item =>
          !item.enterpriseId && !item.scored && (item.dealType === '1' || item.dealType === '3')
        );
        const thirdPerson = operateModule.textList.filter(item =>
          item.enterpriseId && !item.scored && (item.dealType === '1' || item.dealType === '3')
        );
        // 构造评分数据
        const postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "评分完毕",
          variables: {
            addScoreInfo: JSON.stringify({
              writeScore: [
                ...ownPerson.map(({ allParseData, ...rest }) => rest),
                ...thirdPerson.map(({ allParseData, ...rest }) => rest)
              ]
            }),
          },
        }];
        // 先提交评分
        await batchSubmitWithdrawProcess(postData);
        // 再处理转售
        const postData2 = {
          orderId: Route.query.orderId,
          projectCode: data.formDataScore.projectCode,
          projectName: data.formDataScore.projectName,
          deliveryManager: data.formDataScore.deliveryManager,
          sendMsg: data.formDataScore.isTurn === "1",
        };
        await midDispatch(postData2);
        message.success("操作成功");
        handlExistTask();
      } else if (operateModule.status === 'submitPage') {
        // 调度人自己支撑结束流程，并转售中
        const postData2 = {
          orderId: Route.query.orderId,
          projectCode: data.formDataScore.projectCode,
          projectName: data.formDataScore.projectName,
          deliveryManager: data.formDataScore.deliveryManager,
          sendMsg: data.formDataScore.isTurn === "1",
        };
        await midDispatch(postData2);
        message.success("操作成功");
        handlExistTask();
        // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      } else {
        // 发起人结束工单时处理转售中
        // 调度人自己支撑结束流程
        const operateModule = data.formData.moduleForm[data.operateIndex];
        const postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "发起人结束调度",
          variables: {
            suggest: "", // 生态评价
            sendMsg: true, //是否发短信
          },
        }];
        cityBack(postData).then(async (res) => {
          const postData2 = {
            orderId: Route.query.orderId,
            projectCode: data.formDataScore.projectCode,
            projectName: data.formDataScore.projectName,
            deliveryManager: data.formDataScore.deliveryManager,
            sendMsg: data.formDataScore.isTurn === "1",
          };
          await midDispatch(postData2);
          message.success("操作成功");
          handlExistTask();
        });
      }
      // }
      // });
      // batchSubmitWithdrawProcess(postData).then((res) => {
      //   message.success("评分成功");
      //   window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      // });
      // } else {
      //   // 调度人自己支撑结束流程
      //   const postData = data.formData.moduleForm.map((item) => {
      //     return {
      //       taskId: item.taskId,
      //       procInsId: item.procInstId,
      //       comment: "发起人结束调度",
      //       variables: {
      //         sendMsg: true, //是否发短信
      //       },
      //     };
      //   });
      //   cityBack(postData).then((res) => {
      //     message.success(res.msg);
      //     window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      //   });
      // }
    };
    const changeRadio = (e) => {
      console.log(e.target.value, `ookolkio`);
      if (e.target.value == "1") {
        data.isSubmit = true;
      } else {
        data.isSubmit = false;
        data.formDataScore.deliveryManager = undefined;
        data.isSubmit = false;
      }
    };
    const filterOption = (input, option) => {
      return option.label.indexOf(input) >= 0;
    };
    const ecologyChangeOld = (val) => {
      data.CooperateData.contanct = null;
      data.CooperateData.phone = null;
      let list = data.teamOldList.filter((item) => {
        return item.name == val;
      });
      data.CooperateData.totalScore = list[0]?.totalScore;
      data.CooperateData.sync = list[0]?.sync;
      data.CooperateData.auth = list[0]?.auth;
      data.CooperateData.area = list[0]?.address != null ? list[0]?.address : "江苏省";
      data.contanctList = list[0]?.contactList;
      data.CooperateData.enterpriseId = list[0]?.enterpriseId;
      data.CooperateData.ecopartnerId = list[0]?.ecopartnerId;
      if (data.CooperateData.sync == 1 && data.CooperateData.auth == 1) {
        data.isSyncAuth = false;
      } else {
        data.isSyncAuth = true;
      }
    };
    const handleOk = () => { }
    const setFileData = (fileInfo, moduleItem) => {
      moduleItem.fileList = fileInfo.fileList;
    };
    const viewFileData = (view) => {
      data.viewLoading = view;
    };
    const toCompanyDetail = (item) => {
      console.log('item', item);

      if (item.ecopartnerId > 10000) {
        window.open(
          "https://ipartner.jsdict.cn/static/detail?partner_id=" + item.ecopartnerId + "&token=bhubh3333ugy", "_blank"
        );
      } else {
        window.open(
          "https://ipartner.jsdict.cn/static/detail?partner_id=" + item.enterpriseId + "&token=bhubh3333ugy", "_blank"
        );
      }
    };
    const closeModal = () => {
      data.moduleTypeVisible = false;
    };
    const showModuleTypeDialog = (index) => {
      data.moduleTypeVisible = true;
      data.currentModuleIndex = index;
    };
    // 模块选择弹窗确定事件
    const handleSelectModule = (moduleType) => {
      const index = data.currentModuleIndex;
      if (typeof index === 'number' && data.formData.moduleForm[index]) {
        data.formData.moduleForm[index].uid = moduleType.id;
        data.formData.moduleForm[index].moduleName = moduleType.name;
        data.formData.moduleForm[index].intro = moduleType.intro || moduleType.description || moduleType.summary || moduleType.abilityIntro || ""; '';
        formatEcopartnerList(index, moduleType);
      }
      data.moduleTypeVisible = false;
      const exitMainModule = data.formData.moduleForm.find((item) => {
        return item.businessModuleType === '1' && item.moduleName;
      });
      if (exitMainModule) {
        // 有方案，则工单标题以方案开头
        data.formData.title = "关于" + exitMainModule.moduleName + "的售前支撑工单";
      } else {
        for (let i = 0; i < data.formData.moduleForm.length; i++) {
          if (data.formData.moduleForm[i].moduleName) {
            // 没有方案，则工单标题以第一个支撑模块开头
            data.formData.title = "关于" + data.formData.moduleForm[i].moduleName + "的售前支撑工单";
            break;
          }
        }
      }
    };
    const handleEcopartnerInfo = async (phone) => {
      try {
        const val = await getAllUserList({ phone, scope: 0 });
        if (!val.data?.[0]) return;
        const orgInfo = val.data[0].orgAllName?.split(",") || [];
        if (orgInfo.length === 2 && orgInfo[1] === "苏移集成") {
          return "江苏移动信息系统集成有限公司";
        }
        return orgInfo[1] || val.data[0].orgName;
      } catch (error) {
        console.error("获取生态能力方信息失败:", error);
        return null;
      }
    };
    // 格式化生态合作方list
    const formatEcopartnerList = async (index, { ecopartnerList, ecologyType, contact, createBy, phone, provider, name }) => {
      ecopartnerList = ecopartnerList || [];
      // 设置生态类型
      data.formData.moduleForm[index].ecologyType = ecologyType;
      let tempCompany = []; // 自有能力方
      let tempOwnPerson = []; // 生态厂商
      // 根据生态类型过滤数据
      if (ecologyType) {
        if (ecologyType.includes("2")) {
          // 生态厂商
          tempCompany = ecopartnerList.filter(item => item.ecopartnerId !== null);
        }
        if (ecologyType.includes("1")) {
          // 自有能力方
          tempOwnPerson = ecopartnerList.filter(item => item.ecopartnerId == null);
          // 批量处理自有能力方信息
          const promises = tempOwnPerson.map(async (person) => {
            if (person.contactPhone) {
              person.belong = await handleEcopartnerInfo(person.contactPhone);
            }
          });
          await Promise.all(promises);
        }
      }
      // 设置生态伙伴列表（只包含有ecopartnerId的项）
      // data.formData.moduleForm[index].ecopartnerList = tempCompany;
      // 筛选授权且同步的企业ID
      data.companyIdList = data.formData.moduleForm[index].editDataCompany.filter(item => item.auth === 1 && item.sync === 1).map(item => item.enterpriseId);
      // 批量处理联系人列表 - 使用Promise.all优化异步处理
      const contactPromises = data.formData.moduleForm[index].editDataCompany
        .filter(item => item.ecopartnerName) // 只处理有名称的项
        .map(async (item) => {
          try {
            const res = await selectTreeNew(item.ecopartnerName);
            item.contactList = res.data?.[0]?.contactList || [];
          } catch (error) {
            console.error(`获取联系人列表失败: ${item.ecopartnerName}`, error);
            item.contactList = [];
          }
        });
      // 等待所有联系人信息获取完成
      await Promise.all(contactPromises);
      // 解析provider信息
      const providerParts = provider ? provider.split("/") : [];
      const belongDepartment = providerParts[2] || '';
      // 构建模块自有联系人信息
      data.formData.moduleForm[index].ownProvince = [{
        contactName: contact, // 模块联系人
        userId: createBy, // 模块创建人id
        contactPhone: phone, // 模块联系方式
        belong: belongDepartment, // 模块归属部门
      }];
      // 构建售前调度流程表格数据
      data.formData.moduleForm[index].editDataCompany = [{
        name: name, // 模块名称
        company: tempCompany, // 生态厂商
        ownPerson: tempOwnPerson, // 自有能力方
      }];
    };
    const queryEcoparterComment = (dataList, moduleIndex, parsedData) => {
      // 查询生态厂商评论
      // getProcessInfoNew(id).then((res) => {
      if (!parsedData.fileListDeal) parsedData.fileListDeal = []
      // 获取生态厂商反馈信息列表，allData为每个厂商的反馈记录（同意/拒绝/处理中等）
      let allData = dataList;
      const newTextList = allData.map((item) => {
        return {
          ...item,
          allParseData: parsedData,
        };
      });
      let num = allData.length;
      data.formData.moduleForm[moduleIndex].textList = JSON.parse(JSON.stringify(newTextList)); // 用于页面展示所有生态厂商反馈信息
      if (Route.query.type == "reStar") {
        // 如果是省直调度人首次选择合作方，判断是否有反馈信息，决定页面状态
        data.contralSupport = true;// 标记为省直调度人
      }
      // 遍历所有反馈，收集被拒绝的厂商信息，便于后续过滤和重新选择
      allData.forEach((item) => {
        if (item.dealType == "2") { // 2为拒绝
          // 构造被拒绝厂商信息，加入拒绝列表
          let info = {
            ecopartnerName: item.company,
            contactName: item.contactUserName,
            contactPhone: item.contactPhone,
            userId: item.userId,
          };
          data.rejectCompanyIdlist.push(info);
          // 构造可重新选择能力方的数据结构
          let ar = [
            {
              name: data.formData.moduleForm[moduleIndex].editDataCompany[0].name,
              company: data.formData.moduleForm[moduleIndex].editDataCompany[0].company,
              ownPerson: data.formData.moduleForm[moduleIndex].editDataCompany[0].ownPerson,
              ownProvince: data.formData.moduleForm[moduleIndex].ownProvince,
            },
          ];
          data.dataCompanyNew = ar;
          // 过滤掉已被拒绝的联系人，优先选可用联系人
          data.dataCompanyNew[0].company.forEach((item) => {
            if (item.contactList) {
              const availableContact = item.contactList.find((contact) =>
                contact.approve === 1 &&
                !data.rejectCompanyIdlist.some((value) => value.userId == contact.userId)
              );
              if (availableContact) {
                item.contactName = availableContact.contactName;
                item.contactPhone = availableContact.contactPhone;
                item.userId = availableContact.userId;
              }
            }
          });
        }
      });
      // 判断最后一个反馈是否为拒绝，若是则进入重新选择能力方页面
      if (allData[num - 1] && allData[num - 1].dealType == "2") {
        data.reSelectData = allData; // 展示所有反馈信息
        data.reSelectData.forEach((item) => {
          // 构造表格展示用的info字段
          item.info = {
            ecopartnerName: item.company,
            contactName: item.contactUserName,
            contactPhone: item.contactPhone,
          };
          item.dealTime = item.dealTime;
          if (item.dealType == "2") {
            item.dealContent = "拒绝"; // 标记为拒绝
          } else if (item.dealType == "1") {
            item.dealContent = "同意";
          }
        });
      }
      // 初始化评分表，仅对未处理的厂商（dealType==0或null）赋默认分数
      allData.forEach((item) => {
        if (item.dealType == "0" || item.dealType == null) {
          data.formData.moduleForm[moduleIndex].tableData1.push({
            companyData: {
              ecopartnerName: item.company,
              contactName: item.contactUserName,
              contactPhone: item.contactPhone,
            },
            satisfiedScore: 1, // 默认满意度分数
            responseScore: 1, // 默认响应分数
            module: data.formData.moduleForm[moduleIndex].name,
          });
        }
      });
      // 评分和结果展示用的最终反馈数据
      // if (allData[num - 1]) {
      //   data.formData.moduleForm[moduleIndex].editFinishData = allData;
      //   data.formData.moduleForm[moduleIndex].editFinishData.forEach((item) => {
      //     item.info = {
      //       ecopartnerName: item.company,
      //       contactName: item.contactUserName,
      //       contactPhone: item.contactPhone,
      //     };
      //     if (item.dealType == "2") {
      //       item.dealContent = "拒绝";
      //     } else if (item.dealType == "1") {
      //       item.dealContent = "同意";
      //     }
      //     item.dealTime = item.dealTime;
      //   });
      // }
      // 评分页面去重处理：同一公司多条反馈时，优先保留非拒绝项
      if (Route.query.type == "writeScore") {
        for (let i = 0; i < data.formData.moduleForm.length; i++) {
          // 遍历每个模块的textList
          data.formData.moduleForm[i].textList = processDuplicateCompaniesByName(data.formData.moduleForm[i].textList);
          // 同意的排第一，否则按时间最新拒绝的排序
          data.formData.moduleForm[i].textList = sortTextList(data.formData.moduleForm[i].textList)
        }
      }
      // })
    };
    // 删除支撑模块
    const handleDeleteModule = (index) => {
      if (data.formData.moduleForm.length > 1) {
        data.formData.moduleForm.splice(index, 1);
      }
    };
    // 新增支撑模块
    const addModule = () => {
      data.formData.moduleForm.push({
        uid: null,
        procInstId: "",
        taskId: "",
        // editFinishData: [], // 对生态厂商的评分
        textList: [], // 生态厂商列表
        ownProvince: [], //自有联系人list
        tableData1: [],
        projectContent: "",
        fileList: [],
        businessModuleType: "1",
        moduleName: "",
        intro: "",
        selectUsers: [],
        ecologyType: "",
        editDataCompany: [],
        selectCompanyList: {},
        ipartnerId: "",
        selectId: "",
        selectIdOwn: "",
        selectPhone: "",
        needProvince: false, // 是否申请省级调度支撑
        isBySelf: "2", // 1自己支撑2不自己支撑
      });
    };
    const download = (file) => {
      const href = file.url || file.fileUrl;
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let newHref = href;
      if (href.includes(windowOrigin)) {
        newHref = "/portal" + href.split(windowOrigin)[1];
      }
      window.open(windowOrigin + newHref + "?token=" + token);
      return false;
    };
    const view = (file) => {
      data.viewLoading = true;
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      pptTopdf({
        filePath: file.path,
      }).then((res) => {
        if (res.code == 200) {
          data.viewLoading = false;
          let newHref = res.data;
          if (res.data.includes(windowOrigin)) {
            newHref = "/portal" + res.data.split(windowOrigin)[1];
          }
          const newpage = Router.resolve({
            name: "lookPdf",
            query: {
              urlMsg: encodeURIComponent(
                windowOrigin + newHref + "?token=" + token
              ),
              urlName: file.name,
            },
          });
          window.open(newpage.href, "_blank");
        }
      });
      return false;
    };
    const changeBusinessModuleType = (val, index) => {
      const module = data.formData.moduleForm[index];
      if (module) {
        module.uid = null;
        module.businessModuleType = val.target.value;
        module.name = '';
        module.intro = '';
        module.ecologyType = '';
        module.selectUsers = [];
        module.editDataCompany[0].company = [];
        module.editDataCompany[0].ownPerson = [];
        module.editDataCompany[0].ownProvince = [];
      }
    };
    const limitLength = (e) => {
      if (e.target.value.length > 30) {
        data.formData.projectCode = e.target.value.slice(0, 30);
      }
    };
    const handleWriteScore = (operateModule) => {
      let tempTextList = operateModule.textList.filter((i) => i.enterpriseId);
      tempTextList = tempTextList.filter((i) => i.dealType && i.dealType == "1");// 同意的才可以评价
      const hasEmptyScore = tempTextList.some((i) => !i.satisfiedScore || !i.responseScore);
      if (hasEmptyScore) {
        // 自有能力方、自有联系人不支持生态评分，生态厂商需要评分
        message.warning("评分不能为空");
        data.addLoading = false;
        return false;
      }
      querytOrderById(Route.query.orderId).then((res) => {
        const taskList = res.data.todoTasks || [];
        const procCount = res.data.procCount;
        if (procCount == 1) {
          // 单模块直接转售中
          data.formDataScore.projectCode = data.formData.projectCode;
          data.formDataScore.projectName = data.formData.projectName;
          data.formDataScore.deliveryManager = undefined;
          data.formDataScore.isTurn = "2";
          data.showScore = true;
          data.addLoading = false;
          return
        }
        if (taskList.length > 1) {
          // 不是最后一个打分的模块，只评分
          // 只提交同意的且没有评过分的生态方数据
          handleOrderTask();
        } else {
          // 最后一个打分的模块，弹售中弹窗
          if (taskList.length == 1 && taskList[0].taskId == operateModule.taskId) {
            // 最后一个打分的模块，弹售中弹窗
            data.formDataScore.projectCode = data.formData.projectCode;
            data.formDataScore.projectName = data.formData.projectName;
            data.formDataScore.deliveryManager = undefined;
            data.formDataScore.isTurn = "2";
            data.showScore = true;
            data.addLoading = false;
          } else {
            handleOrderTask();
          }
        }
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const submit = async (index) => {
      data.operateIndex = index;
      const operateModule = data.formData.moduleForm[data.operateIndex];
      data.addLoading = true;
      if (operateModule.status == "writeScore") {
        // 评分
        handleWriteScore(operateModule);
      } else {
        data.addLoading = false;
      }
    }
    const handlExistTask = () => {
      operateOrderById(Route.query.orderId).then((res) => {
        let tempProInstList = res.data.procInstList;
        if (tempProInstList && tempProInstList.length > 0) {
          // 该工单仍有待办，操作完成
          actionInitData();
        } else {
          window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog";
        }
      }).finally(() => {
        data.addLoading = false;
      });
    }
    return {
      ...toRefs(data),
      mainFormRef,
      addFormRef,
      scoreFormRef,
      businessModuleTypeComputed,
      toCompanyDetail,
      ecologyChangeOld,
      handleOk,
      setFileData,
      viewFileData,
      submit,
      changeRadio,
      filterOption,
      handleCloseScore,
      handleOkScore,
      endWork,
      dealCon,
      dealMethod,
      formatRealName,
      reSubmit,
      cancel,
      addCooperate,
      closeAdd,
      handleSearch,
      selectUser,
      selectUserCom,
      onCheckChange,
      submitAdd,
      toDetail,
      disabledDate,
      closeModal,
      showModuleTypeDialog,
      handleSelectModule,
      handleDeleteModule,
      addModule,
      view,
      download,
      changeBusinessModuleType,
      limitLength,
      toChinese,
    };
  },
});
</script>
<style lang="scss" scoped>
@import "./reStar.scss";
</style>
<style lang="scss">
.resize-table-header-line.el-table {

  // 默认表头和单元格使用默认光标
  th.el-table__cell,
  td>.cell {
    cursor: default !important;
  }

  // 只在表头分隔线位置显示调整列宽光标
  th.el-table__cell {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 8px; // 分隔线热区宽度
      height: 100%;
      cursor: default;
      transform: translateX(50%); // 居中显示
    }

    &:hover::after {
      cursor: col-resize !important;
    }
  }
}
</style>
