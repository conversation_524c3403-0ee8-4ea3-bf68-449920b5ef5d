import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/dispatchCenter',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "调度中心",
        },
        children: [
            {
                path: 'workbench',
                name: 'workbench',
                meta: {
                    title: "调度中心"
                },
                component: () => import('@/views/dispatchCenter/workbench/index.vue')
            },
            {
                path: 'transfer',
                component: () => import('@/views/dispatchCenter/transfer/index.vue'),
                name: 'transfer',
            },
            {
                path: 'starWork',
                name: 'starWork',
                meta: {
                    title: "工单信息"
                },
                component: () =>
                    import('@/views/dispatchCenter/starWork/index.vue')

            },
            {
                path: 'inWork',
                name: 'inWork',
                meta: {
                    title: "工单信息"
                },
                component: () =>
                    import('@/views/dispatchCenter/inWork/index.vue')

            },
            {
                path: 'reStarWork',
                name: 'reStarWork',
                meta: {
                    title: "工单信息"
                },
                component: () =>
                    import('@/views/dispatchCenter/starWork/reStar.vue')

            },
            {
                path: 'endWork',
                name: 'endWork',
                meta: {
                    title: "已办工单信息"
                },
                component: () =>
                    import('@/views/dispatchCenter/starWork/index.vue')

            },
            // {
            //     path: 'inWorked',
            //     name: 'inWorked',
            //     meta: {
            //         title: "已办工单信息"
            //     },
            //     component: () =>
            //         import('@/views/dispatchCenter/inWork/index.vue')

            // },
            {
                path: 'coordination',
                name: 'coordination',
                meta: {
                    title: "售中调度"
                },
                component: () => import('@/views/dispatchCenter/coordination/index.vue')
            },
        ]
    },
]

export default ROUTES;